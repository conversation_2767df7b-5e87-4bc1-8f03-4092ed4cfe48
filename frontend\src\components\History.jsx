import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

const History = ({ isLoggedIn }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const { session } = useAuth();

  const fetchHistory = useCallback(async () => {
    if (!session?.access_token) return;

    setLoading(true);
    try {
      const response = await fetch('http://127.0.0.1:5000/history', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHistory(data.history || []);
      } else {
        console.error('Failed to fetch history');
      }
    } catch (error) {
      console.error('Error fetching history:', error);
    } finally {
      setLoading(false);
    }
  }, [session?.access_token]);

  useEffect(() => {
    if (isLoggedIn && session) {
      fetchHistory();
    }
  }, [isLoggedIn, session, fetchHistory]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="history">
      {isLoggedIn ? (
        <>
          <h3>Chat History</h3>
          {loading ? (
            <p>Loading...</p>
          ) : history.length > 0 ? (
            <div className="history-list">
              {history.map((chat, index) => (
                <div key={chat.id || index} className="history-item">
                  <div className="history-date">
                    {formatDate(chat.created_at)}
                  </div>
                  <div className="history-message">
                    <strong>You:</strong> {chat.user_message.substring(0, 50)}
                    {chat.user_message.length > 50 ? '...' : ''}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p>No chat history yet. Start a conversation!</p>
          )}
        </>
      ) : (
        <div className="history-logged-out-message">
          <h3>Chat History</h3>
          <p>Please <strong>sign in</strong> to access your chat history.</p>
        </div>
      )}
    </div>
  );
};

export default History;