#!/usr/bin/env python3
"""
Test script to verify Flask endpoints work correctly
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_chat_endpoint():
    """Test the /chat endpoint"""
    try:
        print("🔄 Testing /chat endpoint...")
        
        data = {
            "message": "Hello, this is a test message"
        }
        
        response = requests.post(f"{BASE_URL}/chat", 
                               json=data, 
                               headers={"Content-Type": "application/json"},
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat endpoint works! Response: {result.get('response', '')[:100]}...")
            return True
        else:
            print(f"❌ Chat endpoint failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False

def test_history_endpoint():
    """Test the /history endpoint"""
    try:
        print("🔄 Testing /history endpoint...")
        
        # Test without authentication (should fail)
        response = requests.get(f"{BASE_URL}/history", timeout=10)
        
        if response.status_code == 401:
            print("✅ History endpoint correctly requires authentication")
            return True
        else:
            print(f"❌ History endpoint should require auth but got: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ History endpoint error: {e}")
        return False

def test_server_running():
    """Test if server is running"""
    try:
        print("🔄 Testing if server is running...")
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"✅ Server is running! Status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Server test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Flask Endpoint Tests...")
    print("=" * 50)
    
    # Test 1: Server running
    if not test_server_running():
        print("💡 Make sure to start the Flask server first: python app.py")
        exit(1)
    
    # Test 2: Chat endpoint
    if not test_chat_endpoint():
        exit(1)
    
    # Test 3: History endpoint
    if not test_history_endpoint():
        exit(1)
    
    print("=" * 50)
    print("🎉 All endpoint tests passed!")
