import "../index.css";
import { Auth } from "@supabase/auth-ui-react";
import { ThemeSupa } from "@supabase/auth-ui-shared";
import { useAuth } from "../contexts/AuthContext";
import Header from "../components/header";

export default function SignIn() {
  const { session, signOut, supabase } = useAuth();

  if (!session) {
    return (
      <div className="home-container">
        <Header/>
        <div className="container hero">
          <div className="row">
            <div className="col-md-4">
              <div className="p-4 shadow rounded bg-white auth-card">
                <Auth
                  supabaseClient={supabase}
                  appearance={{ theme: ThemeSupa }}
                  providers={["google", "github"]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="home-container">
        <div className="container hero">
          <div className="row">
            <div className="col-md-4">
              <div className="p-4 shadow rounded bg-white">
                <p style={{ color: "#000" }}>
                  You are successfully logged in as {session.user.email}.
                  Explore the features and start your learning journey.
                </p>
                <button onClick={signOut} className="btn btn-cs">
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
