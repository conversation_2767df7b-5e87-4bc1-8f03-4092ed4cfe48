#!/usr/bin/env python3
"""
Simple test to verify Flask and basic functionality
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import logging
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = "https://bhrwvazkvsebdxstdcow.supabase.co/"
SUPABASE_KEY = os.getenv("VITE_SUPABASE_KEY")

logging.basicConfig(level=logging.INFO)

# Initialize Flask App
app = Flask(__name__)
CORS(app)

# Initialize Supabase
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

@app.route('/', methods=['GET'])
def home():
    return jsonify({"message": "Flask server is running!", "status": "OK"})

@app.route('/test-chat', methods=['POST'])
def test_chat():
    try:
        user_message = request.json.get("message", "")
        if not user_message:
            return jsonify({"error": "No message provided"}), 400
        
        # Simple echo response for testing
        response_message = f"Echo: {user_message}"
        
        # Test database insert
        data = {
            "user_message": user_message,
            "response_message": response_message,
            "user_id": None
        }
        
        db_response = supabase.table("chat_logs").insert(data).execute()
        logging.info(f"Database insert: {db_response}")
        
        return jsonify({"response": response_message}), 200
        
    except Exception as e:
        logging.error(f"Test chat error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/test-history', methods=['GET'])
def test_history():
    try:
        # Get recent chat logs
        response = supabase.table("chat_logs").select("*").order("created_at", desc=True).limit(5).execute()
        
        return jsonify({"history": response.data}), 200
        
    except Exception as e:
        logging.error(f"Test history error: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting simple test server on http://127.0.0.1:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
