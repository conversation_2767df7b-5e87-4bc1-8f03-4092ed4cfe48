#!/usr/bin/env python3
"""
Test script to verify Supabase database connection and operations
"""
import os
import logging
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

SUPABASE_URL = "https://bhrwvazkvsebdxstdcow.supabase.co/"
SUPABASE_KEY = os.getenv("VITE_SUPABASE_KEY")

logging.basicConfig(level=logging.INFO)

def test_supabase_connection():
    """Test basic Supabase connection"""
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Supabase client created successfully")
        return supabase
    except Exception as e:
        print(f"❌ Failed to create Supabase client: {e}")
        return None

def test_chat_logs_table(supabase):
    """Test chat_logs table operations"""
    try:
        # Test insert
        test_data = {
            "user_message": "Test message",
            "response_message": "Test response",
            "user_id": None  # For testing without auth
        }
        
        print("🔄 Testing insert operation...")
        response = supabase.table("chat_logs").insert(test_data).execute()
        print(f"✅ Insert successful: {response.data}")
        
        # Test select
        print("🔄 Testing select operation...")
        response = supabase.table("chat_logs").select("*").limit(5).execute()
        print(f"✅ Select successful: Found {len(response.data)} records")
        
        for record in response.data:
            print(f"   - ID: {record.get('id')}, Message: {record.get('user_message')[:50]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ Database operation failed: {e}")
        return False

def test_table_structure(supabase):
    """Test if chat_logs table has correct structure"""
    try:
        # Try to get table info by selecting with specific columns
        response = supabase.table("chat_logs").select("id, user_message, response_message, user_id, created_at").limit(1).execute()
        print("✅ Table structure is correct")
        return True
    except Exception as e:
        print(f"❌ Table structure issue: {e}")
        print("💡 Make sure chat_logs table exists with columns: id, user_message, response_message, user_id, created_at")
        return False

if __name__ == "__main__":
    print("🚀 Starting Supabase Database Tests...")
    print("=" * 50)
    
    # Test 1: Connection
    supabase = test_supabase_connection()
    if not supabase:
        exit(1)
    
    # Test 2: Table structure
    if not test_table_structure(supabase):
        exit(1)
    
    # Test 3: Basic operations
    if not test_chat_logs_table(supabase):
        exit(1)
    
    print("=" * 50)
    print("🎉 All tests passed! Database is working correctly.")
